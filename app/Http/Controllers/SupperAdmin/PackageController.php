<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Enums\BaseStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PackageRequest;
use App\Models\Category;
use App\Models\Package;
use App\Models\PackageList;
use App\Models\Provider;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PackageController extends Controller
{
    public function index(Request $request)
    {
        \Assets::addScriptsDirectly(['assets/js/supper-admin/datatable-package.min.js']);
        return view('supper-admin.packages.index');
    }
    public function ajaxData()
    {
        $data = Package::query()
            ->select('id', 'name', 'category_id', 'member_price','collaborator_price','agency_price', 'distributor_price','status', 'visibility', 'created_at');

        if (request()->has('order_by') && request()->has('order_dir')) {
            $allowedColumns = ['id', 'name', 'category_id', 'member_price', 'collaborator_price', 'agency_price', 'distributor_price', 'status', 'visibility', 'created_at'];
            $orderBy = request('order_by');
            $orderDir = request('order_dir');
            if (in_array($orderBy, $allowedColumns)) {
                $data->orderBy($orderBy, $orderDir);
            }
        }

        return DataTables::of($data)
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('name', 'like', '%' . request('keyword') . '%');
                }
            })
            ->addColumn('category', function ($row) {
                return $row->category->name;
            })
            ->make(true);
    }
    public function create()
    {
        \Assets::addScriptsDirectly(['assets/js/packages.min.js', 'assets/libs/select2/js/select2.min.js']);
        $categories = Category::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();
        $providers = Provider::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();

        $packageLists = PackageList::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();
        return view('supper-admin.packages.create', [
            'categories' => $categories,
            'providers' => $providers,
            'packageLists' => $packageLists
        ]);
    }

    public function store(PackageRequest $request)
    {
        $package = new Package();

        $package->fill($request->validated());
        $package->refill_type = 'auto';
        $package->mode = $request->input('mode');
        $package->save();

        return $this->httpResponse()->setNextUrl(route('supper-admin.packages.index'))->withCreatedSuccessMessage();
    }

    public function edit(Package $package)
    {

        \Assets::addScriptsDirectly(['assets/js/packages.min.js', 'assets/libs/select2/js/select2.min.js']);
        $categories = Category::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();
        $providers = Provider::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();
        $packageLists = PackageList::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();
        return view('supper-admin.packages.edit', [
            'categories' => $categories,
            'providers' => $providers,
            'dataEdit' =>  $package,
            'packageLists' => $packageLists
        ]);
    }

    public function update(PackageRequest $request, Package $package)
    {
        $package->fill($request->validated());
        $package->refill_type = 'auto';
        $package->mode = $request->input('mode');
        $package->save();

        return $this->httpResponse()->setNextUrl(route('supper-admin.packages.index'))->withUpdatedSuccessMessage();
    }

    public function destroy(Package $package)
    {
        $package->delete();
        return $this->httpResponse()->setNextUrl(route('supper-admin.packages.index'))->withDeletedSuccessMessage();
    }
}
