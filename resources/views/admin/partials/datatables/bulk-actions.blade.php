<div class="bulk-action-bar d-none bg-light border rounded p-3 mb-3">
    <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
            <span class="me-3">
                <strong><span class="selected-count">0</span></strong> mục đ<PERSON> đư<PERSON> chọn
            </span>
        </div>
        
        <div class="d-flex align-items-center gap-2">
            <!-- Bulk Delete Action -->
            <button type="button" class="btn btn-outline-danger btn-sm bulk-action-btn" data-action="delete">
                <i class="ri-delete-bin-2-line me-1"></i>
                X<PERSON>a đã chọn
            </button>
            
            <!-- Bulk Status Actions -->
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success btn-sm bulk-action-btn" data-action="activate">
                    <i class="ri-check-line me-1"></i>
                    <PERSON><PERSON><PERSON> hoạt
                </button>
                <button type="button" class="btn btn-outline-warning btn-sm bulk-action-btn" data-action="deactivate">
                    <i class="ri-close-line me-1"></i>
                    Vô hiệu hóa
                </button>
            </div>
            
            <!-- More Actions Dropdown -->
            <div class="dropdown">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="ri-more-2-line me-1"></i>
                    Thêm hành động
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item bulk-action-btn" href="#" data-action="export">
                            <i class="ri-download-line me-2"></i>
                            Xuất dữ liệu
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item bulk-action-btn" href="#" data-action="duplicate">
                            <i class="ri-file-copy-line me-2"></i>
                            Nhân bản
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item bulk-action-btn text-danger" href="#" data-action="force-delete">
                            <i class="ri-delete-bin-line me-2"></i>
                            Xóa vĩnh viễn
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Clear Selection -->
            <button type="button" class="btn btn-outline-secondary btn-sm" id="clear-selection">
                <i class="ri-close-line me-1"></i>
                Bỏ chọn
            </button>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Clear selection button
    $(document).on('click', '#clear-selection', function() {
        $('.row-checkbox, #select-all-checkbox').prop('checked', false);
        if (typeof bulkActions !== 'undefined') {
            bulkActions.selectedRows.clear();
            bulkActions.updateBulkActionVisibility();
        }
    });
});
</script>
